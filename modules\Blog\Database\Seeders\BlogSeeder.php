<?php

namespace Modules\Blog\Database\Seeders;

use Illuminate\Database\Seeder;
use Modules\Blog\Models\Post;
use App\Models\User;

class BlogSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create a demo user if no users exist
        if (User::count() === 0) {
            $user = User::factory()->create([
                'name' => 'Demo User',
                'email' => '<EMAIL>',
            ]);
        } else {
            $user = User::first();
        }

        // Create sample blog posts
        $posts = [
            [
                'title' => 'Welcome to Our Modular Laravel Architecture',
                'content' => "This is the first post in our demo blog module. This post demonstrates how our modular architecture works in Laravel 12.\n\nOur module system provides:\n- Automatic service provider registration\n- Route auto-loading\n- View namespacing\n- Migration management\n- Configuration merging\n\nEach module is completely self-contained and can be easily moved between projects or disabled without affecting other parts of the application.",
                'excerpt' => 'Learn about our modular Laravel architecture and how it can help you build better applications.',
                'status' => 'published',
                'published_at' => now()->subDays(7),
                'user_id' => $user->id,
            ],
            [
                'title' => 'Building Scalable Applications with Modules',
                'content' => "Scalability is crucial for modern web applications. Our module architecture helps you build applications that can grow with your business needs.\n\nKey benefits include:\n- Separation of concerns\n- Easier testing\n- Better code organization\n- Simplified maintenance\n- Team collaboration\n\nEach module can be developed independently, making it easier for teams to work on different features simultaneously.",
                'excerpt' => 'Discover how modular architecture improves application scalability and team productivity.',
                'status' => 'published',
                'published_at' => now()->subDays(5),
                'user_id' => $user->id,
            ],
            [
                'title' => 'Advanced Module Features',
                'content' => "Our module system includes many advanced features that make development easier and more efficient.\n\nAdvanced features:\n- Automatic asset publishing\n- Translation support\n- Custom middleware registration\n- Event and listener auto-discovery\n- Database factory integration\n\nThese features ensure that your modules are as powerful as they are organized.",
                'excerpt' => 'Explore the advanced features available in our module architecture.',
                'status' => 'published',
                'published_at' => now()->subDays(3),
                'user_id' => $user->id,
            ],
            [
                'title' => 'Testing Your Modules',
                'content' => "Testing is an essential part of any robust application. Our module architecture includes comprehensive testing support.\n\nTesting features:\n- Isolated test environments\n- Module-specific test suites\n- Factory integration\n- Feature and unit test examples\n\nThis ensures that each module can be tested independently and thoroughly.",
                'excerpt' => 'Learn how to effectively test your modules with our built-in testing infrastructure.',
                'status' => 'published',
                'published_at' => now()->subDays(1),
                'user_id' => $user->id,
            ],
            [
                'title' => 'Future Enhancements',
                'content' => "We're constantly working on improving our module architecture. Here are some exciting features coming soon:\n\n- Module dependency management\n- Hot module reloading\n- Visual module manager\n- Module marketplace\n- Performance optimizations\n\nStay tuned for these exciting updates!",
                'excerpt' => 'Get a sneak peek at the exciting features coming to our module architecture.',
                'status' => 'draft',
                'published_at' => null,
                'user_id' => $user->id,
            ],
        ];

        foreach ($posts as $postData) {
            Post::create($postData);
        }

        $this->command->info('Blog module seeded successfully!');
    }
}
