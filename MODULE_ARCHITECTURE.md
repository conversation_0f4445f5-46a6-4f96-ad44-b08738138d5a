# Laravel Module Architecture Documentation

A comprehensive, scalable module architecture for Laravel 12 applications that promotes code organization, reusability, and maintainability.

## Features

- 🏗️ **Modular Structure**: Organize your application into self-contained modules
- 🔄 **Auto-Discovery**: Modules are automatically discovered and registered
- 🎯 **Artisan Commands**: Generate new modules with a single command
- 🧪 **Testing Support**: Comprehensive testing infrastructure for modules
- 📦 **Asset Management**: Automatic asset publishing and management
- 🌐 **Multi-language**: Built-in translation support for modules
- ⚙️ **Configuration**: Module-specific configuration files
- 🚀 **Performance**: Optimized for production environments

## Quick Start

### Creating a New Module

Generate a new module with all the necessary structure:

```bash
php artisan make:module YourModule
```

This creates a complete module structure with:
- Service Provider
- Controller with CRUD methods
- Model with factory
- Routes (web and API)
- Views (index, create, edit, show)
- Migration
- Configuration file
- Test files

### Module Structure

Each module follows this standardized structure:

```
modules/
├── YourModule/
│   ├── Config/
│   │   └── config.php                 # Module configuration
│   ├── Database/
│   │   ├── Factories/                 # Model factories
│   │   ├── Migrations/                # Database migrations
│   │   └── Seeders/                   # Database seeders
│   ├── Http/
│   │   ├── Controllers/               # Controllers
│   │   ├── Middleware/                # Module middleware
│   │   ├── Requests/                  # Form requests
│   │   └── Resources/                 # API resources
│   ├── Models/                        # Eloquent models
│   ├── Providers/
│   │   └── YourModuleServiceProvider.php  # Module service provider
│   ├── Resources/
│   │   ├── assets/                    # CSS, JS, images
│   │   ├── lang/                      # Translations
│   │   └── views/                     # Blade templates
│   ├── Routes/
│   │   ├── api.php                    # API routes
│   │   └── web.php                    # Web routes
│   ├── Services/                      # Business logic services
│   └── Tests/
│       ├── Feature/                   # Feature tests
│       └── Unit/                      # Unit tests
```

## Core Architecture

### ModuleServiceProvider

The main service provider (`App\Support\Module\ModuleServiceProvider`) automatically:
- Discovers all modules in the `modules/` directory
- Registers module service providers
- Loads module routes, views, migrations, and translations
- Merges module configurations

### BaseModule

All module service providers extend `App\Support\Module\BaseModule`, which provides:
- Automatic resource loading
- Configuration management
- Asset publishing
- Translation loading
- Migration handling

### MakeModuleCommand

The `make:module` Artisan command generates:
- Complete module directory structure
- Service provider with proper namespace
- Controller with CRUD methods
- Model with factory
- Routes (web and API)
- Views with Bootstrap styling
- Migration file
- Configuration file
- Test files

## Demo Module: Blog

The project includes a complete Blog module demonstrating all features:

### Features
- Post management (CRUD operations)
- User authentication integration
- Rich text content support
- Featured images
- Draft/Published status
- SEO-friendly URLs (slugs)
- Responsive design
- Comprehensive test coverage

### Accessing the Demo
- Visit `/blog` to see all published posts
- Visit `/home` to see the module architecture overview
- Create an account to add/edit posts

## Usage Examples

### Creating a Product Module

```bash
php artisan make:module Product
```

This generates a complete Product module that you can immediately customize:

1. **Update the model** (`modules/Product/Models/Product.php`)
2. **Customize the migration** (`modules/Product/Database/Migrations/...create_products_table.php`)
3. **Modify the controller** (`modules/Product/Http/Controllers/ProductController.php`)
4. **Update the views** (`modules/Product/Resources/views/`)
5. **Configure the module** (`modules/Product/Config/config.php`)

### Module Configuration

Each module can have its own configuration file:

```php
// modules/YourModule/Config/config.php
return [
    'name' => 'YourModule',
    'enabled' => true,
    'settings' => [
        'per_page' => 10,
        'cache_ttl' => 3600,
    ],
];
```

Access configuration:
```php
config('yourmodule.settings.per_page')
```

### Module Views

Views are automatically namespaced:

```php
// In your controller
return view('yourmodule::index');

// In Blade templates
@extends('layouts.app')
@include('yourmodule::partials.header')
```

### Module Routes

Routes are automatically loaded and can be organized:

```php
// modules/YourModule/Routes/web.php
Route::prefix('yourmodule')
    ->name('yourmodule.')
    ->group(function () {
        Route::resource('items', ItemController::class);
    });
```

## Testing

### Running Module Tests

Run tests for a specific module:
```bash
php artisan test modules/YourModule/Tests/
```

Run all tests:
```bash
php artisan test
```

### Test Structure

Each module includes:
- **Feature Tests**: Test HTTP endpoints and user interactions
- **Unit Tests**: Test individual classes and methods
- **Factory Integration**: Generate test data easily

Example test:
```php
public function test_can_create_item(): void
{
    $this->actingAs(User::factory()->create());
    
    $response = $this->post(route('yourmodule.items.store'), [
        'name' => 'Test Item',
        'description' => 'Test Description',
    ]);
    
    $response->assertRedirect();
    $this->assertDatabaseHas('items', ['name' => 'Test Item']);
}
```

## Best Practices

1. **Keep modules focused**: Each module should have a single responsibility
2. **Use proper namespacing**: Follow PSR-4 standards
3. **Write tests**: Maintain high test coverage for reliability
4. **Document your modules**: Include README files for complex modules
5. **Use factories**: Create factories for all models to support testing
6. **Follow Laravel conventions**: Stick to Laravel naming and structure conventions

## Advanced Features

### Asset Publishing

Modules can publish assets to the public directory:

```bash
php artisan vendor:publish --tag=yourmodule-assets
```

### Configuration Publishing

Publish module configuration files:

```bash
php artisan vendor:publish --tag=yourmodule-config
```

### View Publishing

Publish module views for customization:

```bash
php artisan vendor:publish --tag=yourmodule-views
```

## Troubleshooting

### Common Issues

1. **Module not found**: Ensure the module directory exists and has proper structure
2. **Routes not loading**: Check that the service provider is registered
3. **Views not found**: Verify the view namespace is correct
4. **Autoloading issues**: Run `composer dump-autoload`

### Debug Commands

```bash
# Check registered service providers
php artisan config:show app.providers

# Clear caches
php artisan config:clear
php artisan route:clear
php artisan view:clear

# Regenerate autoload files
composer dump-autoload
```

## Performance Considerations

- Modules are loaded on every request, so keep service providers lightweight
- Use lazy loading for heavy services
- Cache module configurations in production
- Consider module-specific caching strategies

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

## License

This project is open-sourced software licensed under the [MIT license](LICENSE).

---

**Built with Laravel 12 and ❤️**
