<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Str;

class MakeModuleCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'make:module {name : The name of the module}';

    /**
     * The console command description.
     */
    protected $description = 'Create a new module with the standard structure';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $moduleName = Str::studly($this->argument('name'));
        $modulePath = base_path("modules/{$moduleName}");

        if (File::exists($modulePath)) {
            $this->error("Module {$moduleName} already exists!");
            return 1;
        }

        $this->info("Creating module: {$moduleName}");

        // Create module directory structure
        $this->createDirectoryStructure($modulePath);

        // Create module files
        $this->createModuleFiles($moduleName, $modulePath);

        $this->info("Module {$moduleName} created successfully!");
        $this->info("Module path: {$modulePath}");

        return 0;
    }

    /**
     * Create the module directory structure.
     */
    protected function createDirectoryStructure(string $modulePath): void
    {
        $directories = [
            'Config',
            'Database/Factories',
            'Database/Migrations',
            'Database/Seeders',
            'Http/Controllers',
            'Http/Middleware',
            'Http/Requests',
            'Http/Resources',
            'Models',
            'Providers',
            'Resources/assets/css',
            'Resources/assets/js',
            'Resources/lang/en',
            'Resources/views',
            'Routes',
            'Services',
            'Tests/Feature',
            'Tests/Unit',
        ];

        foreach ($directories as $directory) {
            File::makeDirectory("{$modulePath}/{$directory}", 0755, true);
        }
    }

    /**
     * Create the module files.
     */
    protected function createModuleFiles(string $moduleName, string $modulePath): void
    {
        $this->createServiceProvider($moduleName, $modulePath);
        $this->createController($moduleName, $modulePath);
        $this->createModel($moduleName, $modulePath);
        $this->createRoutes($moduleName, $modulePath);
        $this->createViews($moduleName, $modulePath);
        $this->createConfig($moduleName, $modulePath);
        $this->createMigration($moduleName, $modulePath);
        $this->createTest($moduleName, $modulePath);
    }

    /**
     * Create the module service provider.
     */
    protected function createServiceProvider(string $moduleName, string $modulePath): void
    {
        $stub = $this->getServiceProviderStub();
        $content = str_replace(
            ['{{ModuleName}}', '{{moduleName}}'],
            [$moduleName, Str::lower($moduleName)],
            $stub
        );

        File::put("{$modulePath}/Providers/{$moduleName}ServiceProvider.php", $content);
    }

    /**
     * Create a sample controller.
     */
    protected function createController(string $moduleName, string $modulePath): void
    {
        $stub = $this->getControllerStub();
        $content = str_replace(
            ['{{ModuleName}}', '{{moduleName}}'],
            [$moduleName, Str::lower($moduleName)],
            $stub
        );

        File::put("{$modulePath}/Http/Controllers/{$moduleName}Controller.php", $content);
    }

    /**
     * Create a sample model.
     */
    protected function createModel(string $moduleName, string $modulePath): void
    {
        $stub = $this->getModelStub();
        $content = str_replace(
            ['{{ModuleName}}', '{{moduleName}}'],
            [$moduleName, Str::lower($moduleName)],
            $stub
        );

        File::put("{$modulePath}/Models/{$moduleName}.php", $content);
    }

    /**
     * Create route files.
     */
    protected function createRoutes(string $moduleName, string $modulePath): void
    {
        // Web routes
        $webStub = $this->getWebRoutesStub();
        $webContent = str_replace(
            ['{{ModuleName}}', '{{moduleName}}'],
            [$moduleName, Str::lower($moduleName)],
            $webStub
        );
        File::put("{$modulePath}/Routes/web.php", $webContent);

        // API routes
        $apiStub = $this->getApiRoutesStub();
        $apiContent = str_replace(
            ['{{ModuleName}}', '{{moduleName}}'],
            [$moduleName, Str::lower($moduleName)],
            $apiStub
        );
        File::put("{$modulePath}/Routes/api.php", $apiContent);
    }

    /**
     * Create view files.
     */
    protected function createViews(string $moduleName, string $modulePath): void
    {
        $stub = $this->getViewStub();
        $content = str_replace(
            ['{{ModuleName}}', '{{moduleName}}'],
            [$moduleName, Str::lower($moduleName)],
            $stub
        );

        File::put("{$modulePath}/Resources/views/index.blade.php", $content);
    }

    /**
     * Create config file.
     */
    protected function createConfig(string $moduleName, string $modulePath): void
    {
        $stub = $this->getConfigStub();
        $content = str_replace(
            ['{{ModuleName}}', '{{moduleName}}'],
            [$moduleName, Str::lower($moduleName)],
            $stub
        );

        File::put("{$modulePath}/Config/config.php", $content);
    }

    /**
     * Create migration file.
     */
    protected function createMigration(string $moduleName, string $modulePath): void
    {
        $tableName = Str::snake(Str::plural($moduleName));
        $migrationName = "create_{$tableName}_table";
        $timestamp = date('Y_m_d_His');
        
        $stub = $this->getMigrationStub();
        $content = str_replace(
            ['{{ModuleName}}', '{{tableName}}', '{{migrationClass}}'],
            [$moduleName, $tableName, Str::studly($migrationName)],
            $stub
        );

        File::put("{$modulePath}/Database/Migrations/{$timestamp}_{$migrationName}.php", $content);
    }

    /**
     * Create test file.
     */
    protected function createTest(string $moduleName, string $modulePath): void
    {
        $stub = $this->getTestStub();
        $content = str_replace(
            ['{{ModuleName}}', '{{moduleName}}'],
            [$moduleName, Str::lower($moduleName)],
            $stub
        );

        File::put("{$modulePath}/Tests/Feature/{$moduleName}Test.php", $content);
    }

    /**
     * Get the service provider stub.
     */
    protected function getServiceProviderStub(): string
    {
        return '<?php

namespace Modules\{{ModuleName}}\Providers;

use App\Support\Module\BaseModule;

class {{ModuleName}}ServiceProvider extends BaseModule
{
    /**
     * Register module services.
     */
    protected function registerServices(): void
    {
        // Register module specific services here
    }
}';
    }

    /**
     * Get the controller stub.
     */
    protected function getControllerStub(): string
    {
        return '<?php

namespace Modules\{{ModuleName}}\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\View\View;

class {{ModuleName}}Controller extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(): View
    {
        return view("{{moduleName}}::index");
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(): View
    {
        return view("{{moduleName}}::create");
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        // Implementation here
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id): View
    {
        return view("{{moduleName}}::show", compact("id"));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id): View
    {
        return view("{{moduleName}}::edit", compact("id"));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        // Implementation here
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        // Implementation here
    }
}';
    }

    /**
     * Get the model stub.
     */
    protected function getModelStub(): string
    {
        return '<?php

namespace Modules\{{ModuleName}}\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class {{ModuleName}} extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        // Add your fillable attributes here
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        // Add your attribute casts here
    ];
}';
    }

    /**
     * Get the web routes stub.
     */
    protected function getWebRoutesStub(): string
    {
        return '<?php

use Illuminate\Support\Facades\Route;
use Modules\{{ModuleName}}\Http\Controllers\{{ModuleName}}Controller;

Route::prefix("{{moduleName}}")
    ->name("{{moduleName}}.")
    ->group(function () {
        Route::resource("/", {{ModuleName}}Controller::class)->parameters(["" => "{{moduleName}}"]);
    });';
    }

    /**
     * Get the API routes stub.
     */
    protected function getApiRoutesStub(): string
    {
        return '<?php

use Illuminate\Support\Facades\Route;
use Modules\{{ModuleName}}\Http\Controllers\{{ModuleName}}Controller;

Route::prefix("{{moduleName}}")
    ->name("api.{{moduleName}}.")
    ->group(function () {
        Route::apiResource("/", {{ModuleName}}Controller::class)->parameters(["" => "{{moduleName}}"]);
    });';
    }

    /**
     * Get the view stub.
     */
    protected function getViewStub(): string
    {
        return '@extends("layouts.app")

@section("content")
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">{{ __("{{ModuleName}} Module") }}</div>

                <div class="card-body">
                    <h1>Welcome to {{ModuleName}} Module</h1>
                    <p>This is the index page for the {{ModuleName}} module.</p>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection';
    }

    /**
     * Get the config stub.
     */
    protected function getConfigStub(): string
    {
        return '<?php

return [
    /*
    |--------------------------------------------------------------------------
    | {{ModuleName}} Module Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains the configuration options for the {{ModuleName}} module.
    |
    */

    "name" => "{{ModuleName}}",
    "description" => "{{ModuleName}} module for the application",
    "version" => "1.0.0",
    "enabled" => true,
];';
    }

    /**
     * Get the migration stub.
     */
    protected function getMigrationStub(): string
    {
        return '<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create("{{tableName}}", function (Blueprint $table) {
            $table->id();
            $table->string("name");
            $table->text("description")->nullable();
            $table->boolean("is_active")->default(true);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists("{{tableName}}");
    }
};';
    }

    /**
     * Get the test stub.
     */
    protected function getTestStub(): string
    {
        return '<?php

namespace Modules\{{ModuleName}}\Tests\Feature;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;

class {{ModuleName}}Test extends TestCase
{
    use RefreshDatabase;

    /**
     * Test the module index page.
     */
    public function test_{{moduleName}}_index_page_loads(): void
    {
        $response = $this->get(route("{{moduleName}}.index"));

        $response->assertStatus(200);
    }

    /**
     * Test module functionality.
     */
    public function test_{{moduleName}}_functionality(): void
    {
        // Add your tests here
        $this->assertTrue(true);
    }
}';
    }
}
