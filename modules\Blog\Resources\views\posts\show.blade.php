@extends('layouts.app')

@section('title', $post->title)

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <article class="card">
                @if($post->featured_image)
                    <img src="{{ $post->featured_image }}" class="card-img-top" alt="{{ $post->title }}" style="height: 300px; object-fit: cover;">
                @endif
                
                <div class="card-body">
                    <h1 class="card-title">{{ $post->title }}</h1>
                    
                    <div class="mb-3">
                        <small class="text-muted">
                            By {{ $post->user->name }} • 
                            {{ $post->published_at->format('F d, Y \a\t g:i A') }}
                        </small>
                        
                        @if($post->status === 'draft')
                            <span class="badge bg-warning ms-2">Draft</span>
                        @endif
                    </div>
                    
                    <div class="content">
                        {!! nl2br(e($post->content)) !!}
                    </div>
                </div>
                
                @auth
                    @if(auth()->id() === $post->user_id)
                        <div class="card-footer">
                            <div class="btn-group" role="group">
                                <a href="{{ route('blog.posts.edit', $post) }}" class="btn btn-primary">
                                    Edit Post
                                </a>
                                <form action="{{ route('blog.posts.destroy', $post) }}" method="POST" class="d-inline">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" class="btn btn-danger" 
                                            onclick="return confirm('Are you sure you want to delete this post?')">
                                        Delete Post
                                    </button>
                                </form>
                            </div>
                        </div>
                    @endif
                @endauth
            </article>
            
            <div class="mt-3">
                <a href="{{ route('blog.posts.index') }}" class="btn btn-outline-secondary">
                    ← Back to Posts
                </a>
            </div>
        </div>
    </div>
</div>
@endsection
