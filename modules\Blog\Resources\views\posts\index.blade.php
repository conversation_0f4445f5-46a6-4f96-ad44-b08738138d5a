@extends('layouts.app')

@section('title', 'Blog Posts')

@section('content')
<div class="container">
    <div class="row">
        <div class="col-md-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1>Blog Posts</h1>
                @auth
                    <a href="{{ route('blog.posts.create') }}" class="btn btn-primary">
                        Create New Post
                    </a>
                @endauth
            </div>

            @if($posts->count() > 0)
                <div class="row">
                    @foreach($posts as $post)
                        <div class="col-md-6 mb-4">
                            <div class="card h-100">
                                @if($post->featured_image)
                                    <img src="{{ $post->featured_image }}" class="card-img-top" alt="{{ $post->title }}" style="height: 200px; object-fit: cover;">
                                @endif
                                
                                <div class="card-body d-flex flex-column">
                                    <h5 class="card-title">
                                        <a href="{{ route('blog.posts.show', $post) }}" class="text-decoration-none">
                                            {{ $post->title }}
                                        </a>
                                    </h5>
                                    
                                    @if($post->excerpt)
                                        <p class="card-text">{{ $post->excerpt }}</p>
                                    @else
                                        <p class="card-text">{{ Str::limit(strip_tags($post->content), 150) }}</p>
                                    @endif
                                    
                                    <div class="mt-auto">
                                        <small class="text-muted">
                                            By {{ $post->user->name }} • 
                                            {{ $post->published_at->format('M d, Y') }}
                                        </small>
                                    </div>
                                </div>
                                
                                @auth
                                    @if(auth()->id() === $post->user_id)
                                        <div class="card-footer">
                                            <div class="btn-group" role="group">
                                                <a href="{{ route('blog.posts.edit', $post) }}" class="btn btn-sm btn-outline-primary">
                                                    Edit
                                                </a>
                                                <form action="{{ route('blog.posts.destroy', $post) }}" method="POST" class="d-inline">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="submit" class="btn btn-sm btn-outline-danger" 
                                                            onclick="return confirm('Are you sure you want to delete this post?')">
                                                        Delete
                                                    </button>
                                                </form>
                                            </div>
                                        </div>
                                    @endif
                                @endauth
                            </div>
                        </div>
                    @endforeach
                </div>

                <div class="d-flex justify-content-center">
                    {{ $posts->links() }}
                </div>
            @else
                <div class="text-center py-5">
                    <h3>No posts found</h3>
                    <p class="text-muted">There are no published posts yet.</p>
                    @auth
                        <a href="{{ route('blog.posts.create') }}" class="btn btn-primary">
                            Create the first post
                        </a>
                    @endauth
                </div>
            @endif
        </div>
    </div>
</div>
@endsection
