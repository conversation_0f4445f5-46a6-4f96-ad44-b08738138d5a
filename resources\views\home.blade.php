@extends('layouts.app')

@section('title', 'Laravel Module Architecture')

@section('content')
<div class="container">
    <div class="row">
        <div class="col-md-12">
            <!-- Hero Section -->
            <div class="jumbotron bg-primary text-white text-center py-5 mb-5 rounded">
                <h1 class="display-4">Laravel Module Architecture</h1>
                <p class="lead">A scalable, modular architecture for Laravel {{ app()->version() }} applications</p>
                <hr class="my-4 bg-white">
                <p>Build maintainable applications with our comprehensive module system</p>
                <a class="btn btn-light btn-lg" href="{{ route('blog.posts.index') }}" role="button">
                    Explore Demo Module
                </a>
            </div>

            <!-- Features Grid -->
            <div class="row mb-5">
                <div class="col-md-4 mb-4">
                    <div class="card h-100">
                        <div class="card-body text-center">
                            <div class="mb-3">
                                <i class="fas fa-cubes fa-3x text-primary"></i>
                            </div>
                            <h5 class="card-title">Modular Structure</h5>
                            <p class="card-text">
                                Organize your application into self-contained modules with their own controllers, models, views, and routes.
                            </p>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4 mb-4">
                    <div class="card h-100">
                        <div class="card-body text-center">
                            <div class="mb-3">
                                <i class="fas fa-magic fa-3x text-success"></i>
                            </div>
                            <h5 class="card-title">Auto-Discovery</h5>
                            <p class="card-text">
                                Modules are automatically discovered and registered. Just create a module and it's ready to use.
                            </p>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4 mb-4">
                    <div class="card h-100">
                        <div class="card-body text-center">
                            <div class="mb-3">
                                <i class="fas fa-terminal fa-3x text-info"></i>
                            </div>
                            <h5 class="card-title">Artisan Commands</h5>
                            <p class="card-text">
                                Generate new modules with a single command. Complete with controllers, models, views, and tests.
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Module Structure -->
            <div class="row mb-5">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Module Structure</h5>
                        </div>
                        <div class="card-body">
                            <pre class="bg-light p-3 rounded"><code>modules/
├── YourModule/
│   ├── Config/
│   │   └── config.php
│   ├── Database/
│   │   ├── Factories/
│   │   ├── Migrations/
│   │   └── Seeders/
│   ├── Http/
│   │   ├── Controllers/
│   │   ├── Middleware/
│   │   ├── Requests/
│   │   └── Resources/
│   ├── Models/
│   ├── Providers/
│   │   └── YourModuleServiceProvider.php
│   ├── Resources/
│   │   ├── assets/
│   │   ├── lang/
│   │   └── views/
│   ├── Routes/
│   │   ├── api.php
│   │   └── web.php
│   ├── Services/
│   └── Tests/
│       ├── Feature/
│       └── Unit/</code></pre>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Getting Started</h5>
                        </div>
                        <div class="card-body">
                            <h6>1. Create a New Module</h6>
                            <pre class="bg-light p-2 rounded"><code>php artisan make:module YourModule</code></pre>
                            
                            <h6 class="mt-3">2. Module Features</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check text-success"></i> Auto-loaded routes</li>
                                <li><i class="fas fa-check text-success"></i> Namespaced views</li>
                                <li><i class="fas fa-check text-success"></i> Database migrations</li>
                                <li><i class="fas fa-check text-success"></i> Configuration files</li>
                                <li><i class="fas fa-check text-success"></i> Service providers</li>
                                <li><i class="fas fa-check text-success"></i> Test structure</li>
                            </ul>
                            
                            <h6 class="mt-3">3. Demo Module</h6>
                            <p>Check out our <strong>Blog</strong> module as a complete example:</p>
                            <a href="{{ route('blog.posts.index') }}" class="btn btn-primary">
                                View Blog Module
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Technical Details -->
            <div class="row">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Technical Implementation</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6>Core Components</h6>
                                    <ul>
                                        <li><strong>ModuleServiceProvider:</strong> Auto-discovers and registers modules</li>
                                        <li><strong>BaseModule:</strong> Abstract class for module service providers</li>
                                        <li><strong>MakeModuleCommand:</strong> Artisan command for generating modules</li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <h6>Features</h6>
                                    <ul>
                                        <li>PSR-4 autoloading for modules</li>
                                        <li>Automatic route registration</li>
                                        <li>View namespace registration</li>
                                        <li>Migration auto-loading</li>
                                        <li>Configuration merging</li>
                                        <li>Asset publishing</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('styles')
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
<style>
    .jumbotron {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }
    
    pre code {
        font-size: 0.85rem;
        line-height: 1.4;
    }
    
    .card {
        transition: transform 0.2s;
    }
    
    .card:hover {
        transform: translateY(-2px);
    }
</style>
@endpush
@endsection
