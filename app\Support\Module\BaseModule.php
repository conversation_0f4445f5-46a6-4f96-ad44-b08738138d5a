<?php

namespace App\Support\Module;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Str;

abstract class BaseModule extends ServiceProvider
{
    /**
     * The module name.
     */
    protected string $moduleName;

    /**
     * The module path.
     */
    protected string $modulePath;

    /**
     * Create a new module instance.
     */
    public function __construct($app)
    {
        parent::__construct($app);
        
        $this->moduleName = $this->getModuleName();
        $this->modulePath = $this->getModulePath();
    }

    /**
     * Register the module services.
     */
    public function register(): void
    {
        $this->registerConfig();
        $this->registerServices();
    }

    /**
     * Bootstrap the module services.
     */
    public function boot(): void
    {
        $this->bootRoutes();
        $this->bootViews();
        $this->bootMigrations();
        $this->bootTranslations();
        $this->bootPublishing();
    }

    /**
     * Get the module name.
     */
    protected function getModuleName(): string
    {
        $reflection = new \ReflectionClass($this);
        $namespace = $reflection->getNamespaceName();
        
        // Extract module name from namespace (e.g., Modules\Blog\Providers -> Blog)
        $parts = explode('\\', $namespace);
        return $parts[1] ?? 'Unknown';
    }

    /**
     * Get the module path.
     */
    protected function getModulePath(): string
    {
        return base_path("modules/{$this->moduleName}");
    }

    /**
     * Register module configuration.
     */
    protected function registerConfig(): void
    {
        $configPath = $this->modulePath . '/Config';
        
        if (File::exists($configPath)) {
            $configFiles = File::files($configPath);
            
            foreach ($configFiles as $configFile) {
                $configName = Str::lower($this->moduleName) . '.' . $configFile->getFilenameWithoutExtension();
                $this->mergeConfigFrom($configFile->getPathname(), $configName);
            }
        }
    }

    /**
     * Register module services.
     * Override this method in your module to register custom services.
     */
    protected function registerServices(): void
    {
        // Override in module service provider
    }

    /**
     * Boot module routes.
     */
    protected function bootRoutes(): void
    {
        $routesPath = $this->modulePath . '/Routes';
        
        if (File::exists($routesPath)) {
            // Load web routes
            $webRoutesFile = $routesPath . '/web.php';
            if (File::exists($webRoutesFile)) {
                $this->loadRoutesFrom($webRoutesFile);
            }

            // Load API routes
            $apiRoutesFile = $routesPath . '/api.php';
            if (File::exists($apiRoutesFile)) {
                $this->loadRoutesFrom($apiRoutesFile);
            }
        }
    }

    /**
     * Boot module views.
     */
    protected function bootViews(): void
    {
        $viewsPath = $this->modulePath . '/Resources/views';
        
        if (File::exists($viewsPath)) {
            $this->loadViewsFrom($viewsPath, Str::lower($this->moduleName));
        }
    }

    /**
     * Boot module migrations.
     */
    protected function bootMigrations(): void
    {
        $migrationsPath = $this->modulePath . '/Database/Migrations';
        
        if (File::exists($migrationsPath)) {
            $this->loadMigrationsFrom($migrationsPath);
        }
    }

    /**
     * Boot module translations.
     */
    protected function bootTranslations(): void
    {
        $translationsPath = $this->modulePath . '/Resources/lang';
        
        if (File::exists($translationsPath)) {
            $this->loadTranslationsFrom($translationsPath, Str::lower($this->moduleName));
        }
    }

    /**
     * Boot module publishing.
     */
    protected function bootPublishing(): void
    {
        if ($this->app->runningInConsole()) {
            $this->publishConfig();
            $this->publishViews();
            $this->publishAssets();
            $this->publishMigrations();
        }
    }

    /**
     * Publish module configuration.
     */
    protected function publishConfig(): void
    {
        $configPath = $this->modulePath . '/Config';
        
        if (File::exists($configPath)) {
            $configFiles = File::files($configPath);
            
            foreach ($configFiles as $configFile) {
                $this->publishes([
                    $configFile->getPathname() => config_path(Str::lower($this->moduleName) . '_' . $configFile->getFilename())
                ], Str::lower($this->moduleName) . '-config');
            }
        }
    }

    /**
     * Publish module views.
     */
    protected function publishViews(): void
    {
        $viewsPath = $this->modulePath . '/Resources/views';
        
        if (File::exists($viewsPath)) {
            $this->publishes([
                $viewsPath => resource_path('views/vendor/' . Str::lower($this->moduleName))
            ], Str::lower($this->moduleName) . '-views');
        }
    }

    /**
     * Publish module assets.
     */
    protected function publishAssets(): void
    {
        $assetsPath = $this->modulePath . '/Resources/assets';
        
        if (File::exists($assetsPath)) {
            $this->publishes([
                $assetsPath => public_path('vendor/' . Str::lower($this->moduleName))
            ], Str::lower($this->moduleName) . '-assets');
        }
    }

    /**
     * Publish module migrations.
     */
    protected function publishMigrations(): void
    {
        $migrationsPath = $this->modulePath . '/Database/Migrations';
        
        if (File::exists($migrationsPath)) {
            $this->publishes([
                $migrationsPath => database_path('migrations')
            ], Str::lower($this->moduleName) . '-migrations');
        }
    }
}
