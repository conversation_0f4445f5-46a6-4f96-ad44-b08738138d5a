<?php

use Illuminate\Support\Facades\Route;
use Modules\Blog\Http\Controllers\PostController;

Route::prefix('blog')
    ->name('api.blog.')
    ->group(function () {
        // Public API routes
        Route::get('/posts', [PostController::class, 'index'])->name('posts.index');
        Route::get('/posts/{post}', [PostController::class, 'show'])->name('posts.show');
        
        // Authenticated API routes
        Route::middleware('auth:sanctum')->group(function () {
            Route::post('/posts', [PostController::class, 'store'])->name('posts.store');
            Route::put('/posts/{post}', [PostController::class, 'update'])->name('posts.update');
            Route::delete('/posts/{post}', [PostController::class, 'destroy'])->name('posts.destroy');
        });
    });
