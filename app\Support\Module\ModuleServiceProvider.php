<?php

namespace App\Support\Module;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Str;

class ModuleServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        $this->registerModules();
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        $this->bootModules();
    }

    /**
     * Register all modules found in the modules directory.
     */
    protected function registerModules(): void
    {
        $modulesPath = base_path('modules');
        
        if (!File::exists($modulesPath)) {
            return;
        }

        $modules = File::directories($modulesPath);

        foreach ($modules as $modulePath) {
            $moduleName = basename($modulePath);
            $this->registerModule($moduleName, $modulePath);
        }
    }

    /**
     * Register a single module.
     */
    protected function registerModule(string $moduleName, string $modulePath): void
    {
        // Register module service provider if it exists
        $providerPath = $modulePath . '/Providers/' . $moduleName . 'ServiceProvider.php';
        if (File::exists($providerPath)) {
            $providerClass = "Modules\\{$moduleName}\\Providers\\{$moduleName}ServiceProvider";
            if (class_exists($providerClass)) {
                $this->app->register($providerClass);
            }
        }

        // Register module configuration
        $configPath = $modulePath . '/Config';
        if (File::exists($configPath)) {
            $configFiles = File::files($configPath);
            foreach ($configFiles as $configFile) {
                $configName = Str::lower($moduleName) . '.' . $configFile->getFilenameWithoutExtension();
                $this->mergeConfigFrom($configFile->getPathname(), $configName);
            }
        }
    }

    /**
     * Boot all modules.
     */
    protected function bootModules(): void
    {
        $modulesPath = base_path('modules');
        
        if (!File::exists($modulesPath)) {
            return;
        }

        $modules = File::directories($modulesPath);

        foreach ($modules as $modulePath) {
            $moduleName = basename($modulePath);
            $this->bootModule($moduleName, $modulePath);
        }
    }

    /**
     * Boot a single module.
     */
    protected function bootModule(string $moduleName, string $modulePath): void
    {
        // Load module routes
        $this->loadModuleRoutes($moduleName, $modulePath);
        
        // Load module views
        $this->loadModuleViews($moduleName, $modulePath);
        
        // Load module migrations
        $this->loadModuleMigrations($modulePath);
        
        // Load module translations
        $this->loadModuleTranslations($moduleName, $modulePath);
    }

    /**
     * Load module routes.
     */
    protected function loadModuleRoutes(string $moduleName, string $modulePath): void
    {
        $routesPath = $modulePath . '/Routes';
        
        if (File::exists($routesPath)) {
            // Load web routes
            $webRoutesFile = $routesPath . '/web.php';
            if (File::exists($webRoutesFile)) {
                $this->loadRoutesFrom($webRoutesFile);
            }

            // Load API routes
            $apiRoutesFile = $routesPath . '/api.php';
            if (File::exists($apiRoutesFile)) {
                $this->loadRoutesFrom($apiRoutesFile);
            }
        }
    }

    /**
     * Load module views.
     */
    protected function loadModuleViews(string $moduleName, string $modulePath): void
    {
        $viewsPath = $modulePath . '/Resources/views';
        
        if (File::exists($viewsPath)) {
            $this->loadViewsFrom($viewsPath, Str::lower($moduleName));
        }
    }

    /**
     * Load module migrations.
     */
    protected function loadModuleMigrations(string $modulePath): void
    {
        $migrationsPath = $modulePath . '/Database/Migrations';
        
        if (File::exists($migrationsPath)) {
            $this->loadMigrationsFrom($migrationsPath);
        }
    }

    /**
     * Load module translations.
     */
    protected function loadModuleTranslations(string $moduleName, string $modulePath): void
    {
        $translationsPath = $modulePath . '/Resources/lang';
        
        if (File::exists($translationsPath)) {
            $this->loadTranslationsFrom($translationsPath, Str::lower($moduleName));
        }
    }
}
