<?php

namespace Modules\Blog\Tests\Feature;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Models\User;
use Modules\Blog\Models\Post;

class PostControllerTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->user = User::factory()->create();
    }

    public function test_can_view_posts_index(): void
    {
        // Create some published posts
        Post::factory()->count(3)->create([
            'status' => 'published',
            'published_at' => now()->subDay(),
        ]);

        $response = $this->get(route('blog.posts.index'));

        $response->assertStatus(200);
        $response->assertViewIs('blog::posts.index');
    }

    public function test_can_view_published_post(): void
    {
        $post = Post::factory()->create([
            'status' => 'published',
            'published_at' => now()->subDay(),
        ]);

        $response = $this->get(route('blog.posts.show', $post));

        $response->assertStatus(200);
        $response->assertViewIs('blog::posts.show');
        $response->assertSee($post->title);
    }

    public function test_cannot_view_draft_post_as_guest(): void
    {
        $post = Post::factory()->create([
            'status' => 'draft',
        ]);

        $response = $this->get(route('blog.posts.show', $post));

        $response->assertStatus(404);
    }

    public function test_authenticated_user_can_create_post(): void
    {
        $this->actingAs($this->user);

        $response = $this->get(route('blog.posts.create'));

        $response->assertStatus(200);
        $response->assertViewIs('blog::posts.create');
    }

    public function test_can_store_new_post(): void
    {
        $this->actingAs($this->user);

        $postData = [
            'title' => 'Test Post',
            'content' => 'This is a test post content.',
            'excerpt' => 'Test excerpt',
            'status' => 'published',
        ];

        $response = $this->post(route('blog.posts.store'), $postData);

        $this->assertDatabaseHas('posts', [
            'title' => 'Test Post',
            'slug' => 'test-post',
            'user_id' => $this->user->id,
        ]);

        $post = Post::where('title', 'Test Post')->first();
        $response->assertRedirect(route('blog.posts.show', $post));
    }

    public function test_can_update_own_post(): void
    {
        $this->actingAs($this->user);

        $post = Post::factory()->create([
            'user_id' => $this->user->id,
        ]);

        $updateData = [
            'title' => 'Updated Post Title',
            'content' => 'Updated content',
            'status' => 'published',
        ];

        $response = $this->put(route('blog.posts.update', $post), $updateData);

        $this->assertDatabaseHas('posts', [
            'id' => $post->id,
            'title' => 'Updated Post Title',
            'slug' => 'updated-post-title',
        ]);

        $response->assertRedirect(route('blog.posts.show', $post->fresh()));
    }

    public function test_can_delete_own_post(): void
    {
        $this->actingAs($this->user);

        $post = Post::factory()->create([
            'user_id' => $this->user->id,
        ]);

        $response = $this->delete(route('blog.posts.destroy', $post));

        $this->assertDatabaseMissing('posts', [
            'id' => $post->id,
        ]);

        $response->assertRedirect(route('blog.posts.index'));
    }

    public function test_guest_cannot_create_post(): void
    {
        $response = $this->get(route('blog.posts.create'));

        $response->assertRedirect(route('login'));
    }

    public function test_validation_errors_on_invalid_post_data(): void
    {
        $this->actingAs($this->user);

        $response = $this->post(route('blog.posts.store'), [
            'title' => '', // Required field
            'content' => '', // Required field
            'status' => 'invalid', // Invalid status
        ]);

        $response->assertSessionHasErrors(['title', 'content', 'status']);
    }
}
